// =====================================================================
//  CustomSmudgeImageView.m
// =====================================================================

#import "CustomSmudgeImageView.h"

@interface CustomSmudgeImageView ()
@property (nonatomic, strong, readwrite) UIImage *originalImage;
@property (nonatomic, strong, readwrite) UIImage *previewImage;
@property (nonatomic, strong) UIImage *maskImageInternal;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *history; // {preview,mask}
@property (nonatomic, assign) CGPoint lastPoint;
@end

@implementation CustomSmudgeImageView

#pragma mark - Init

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.userInteractionEnabled = YES;
        self.contentMode   = UIViewContentModeScaleAspectFit;
        _lineWidth         = 30.0;
        _toolMode          = SmudgeToolModeErase;
        _history           = [NSMutableArray array];
    }
    return self;
}

#pragma mark - Getter

- (UIImage *)maskImage    { return self.maskImageInternal; }
- (UIImage *)previewImage { return self.image; }

#pragma mark - Public API

- (void)configureWithOriginalImage:(UIImage *)image mask:(nullable UIImage *)mask {
    if (!image) return;
    _originalImage = image;

    if (mask) {
        self.maskImageInternal = mask;
    } else {
        CGSize sz = image.size;
        UIGraphicsBeginImageContextWithOptions(sz, YES, image.scale);
        [[UIColor blackColor] setFill];
        UIRectFill(CGRectMake(0, 0, sz.width, sz.height));
        self.maskImageInternal = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }

    self.image        = [self.class previewFromOriginal:image mask:self.maskImageInternal];
    self.previewImage = self.image;
    [self.history removeAllObjects];
}

- (void)undoLastSmudge {
    if (self.history.count == 0) return;
    NSDictionary *snap = self.history.lastObject;
    self.image            = snap[@"preview"];
    self.previewImage     = self.image;
    self.maskImageInternal = snap[@"mask"];
    [self.history removeLastObject];
}

- (void)restoreOriginal {
    [self configureWithOriginalImage:self.originalImage mask:nil];
}

#pragma mark - Touches

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    UITouch *t = touches.anyObject;
    self.lastPoint = [t locationInView:self];
    // 保存快照
    [self.history addObject:@{ @"preview": self.image ?: [UIImage new],
                               @"mask"   : self.maskImageInternal ?: [UIImage new] }];
}

- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    UITouch *t = touches.anyObject;
    CGPoint cur = [t locationInView:self];
    [self drawStrokeFrom:self.lastPoint to:cur];
    self.lastPoint = cur;
}

#pragma mark - Drawing Core

- (void)drawStrokeFrom:(CGPoint)start to:(CGPoint)end {
    if (!self.originalImage) return;

    // 1. 计算图片坐标
    CGRect imgFrame = [self contentFrameForImage:self.originalImage inView:self];
    CGSize sz = self.originalImage.size;
    CGFloat sx = sz.width  / imgFrame.size.width;
    CGFloat sy = sz.height / imgFrame.size.height;
    CGPoint sImg = CGPointMake((start.x - imgFrame.origin.x) * sx,
                               (start.y - imgFrame.origin.y) * sy);
    CGPoint eImg = CGPointMake((end.x   - imgFrame.origin.x) * sx,
                               (end.y   - imgFrame.origin.y) * sy);

    if (self.toolMode == SmudgeToolModeErase) {
        // 擦除 = 预览清空透明 + mask 画白
        self.image = [self.class clearLineIn:self.image from:sImg to:eImg width:self.lineWidth];
        self.maskImageInternal = [self.class drawLineIn:self.maskImageInternal from:sImg to:eImg width:self.lineWidth color:UIColor.whiteColor];
    } else {
        // 恢复 = 将原图绘回 + mask 画黑
        self.image = [self.class restoreLineInPreview:self.image original:self.originalImage from:sImg to:eImg width:self.lineWidth];
        self.maskImageInternal = [self.class drawLineIn:self.maskImageInternal from:sImg to:eImg width:self.lineWidth color:UIColor.blackColor];
    }
    self.previewImage = self.image;
}

#pragma mark - Static Helpers

+ (UIImage *)previewFromOriginal:(UIImage *)img mask:(UIImage *)mask {
    CGSize sz = img.size;
    UIGraphicsBeginImageContextWithOptions(sz, NO, img.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    [img drawAtPoint:CGPointZero];
    CGContextSaveGState(ctx);
    CGContextTranslateCTM(ctx, 0, sz.height);
    CGContextScaleCTM(ctx, 1.0, -1.0);
    CGContextClipToMask(ctx, CGRectMake(0, 0, sz.width, sz.height), mask.CGImage);
    CGContextClearRect(ctx, CGRectMake(0, 0, sz.width, sz.height));
    CGContextRestoreGState(ctx);
    UIImage *preview = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return preview;
}

+ (UIImage *)clearLineIn:(UIImage *)src from:(CGPoint)s to:(CGPoint)e width:(CGFloat)w {
    UIGraphicsBeginImageContextWithOptions(src.size, NO, src.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    [src drawAtPoint:CGPointZero];
    CGContextSetBlendMode(ctx, kCGBlendModeClear);
    CGContextSetLineCap(ctx, kCGLineCapRound);
    CGContextSetLineWidth(ctx, w);
    CGContextMoveToPoint(ctx, s.x, s.y);
    CGContextAddLineToPoint(ctx, e.x, e.y);
    CGContextStrokePath(ctx);
    UIImage *res = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return res;
}

+ (UIImage *)restoreLineInPreview:(UIImage *)preview original:(UIImage *)orig from:(CGPoint)s to:(CGPoint)e width:(CGFloat)w {
    UIGraphicsBeginImageContextWithOptions(preview.size, NO, preview.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    // 先绘制当前预览
    [preview drawAtPoint:CGPointZero];
    // 在路径内绘回原图
    CGContextSaveGState(ctx);
    CGContextSetLineCap(ctx, kCGLineCapRound);
    CGContextSetLineWidth(ctx, w);
    CGContextBeginPath(ctx);
    CGContextMoveToPoint(ctx, s.x, s.y);
    CGContextAddLineToPoint(ctx, e.x, e.y);
    CGContextReplacePathWithStrokedPath(ctx);
    CGContextClip(ctx);
    [orig drawAtPoint:CGPointZero];
    CGContextRestoreGState(ctx);
    UIImage *res = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return res;
}

+ (UIImage *)drawLineIn:(UIImage *)src from:(CGPoint)s to:(CGPoint)e width:(CGFloat)w color:(UIColor *)color {
    UIGraphicsBeginImageContextWithOptions(src.size, YES, src.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    [src drawAtPoint:CGPointZero];
    CGContextSetStrokeColorWithColor(ctx, color.CGColor);
    CGContextSetBlendMode(ctx, kCGBlendModeNormal);
    CGContextSetLineCap(ctx, kCGLineCapRound);
    CGContextSetLineWidth(ctx, w);
    CGContextMoveToPoint(ctx, s.x, s.y);
    CGContextAddLineToPoint(ctx, e.x, e.y);
    CGContextStrokePath(ctx);
    UIImage *res = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return res;
}

- (CGRect)contentFrameForImage:(UIImage *)image inView:(UIView *)view {
    CGSize img = image.size, vs = view.bounds.size;
    CGFloat ir = img.width / img.height, vr = vs.width / vs.height;
    if (ir > vr) {
        CGFloat w = vs.width, h = w / ir, y = (vs.height - h) * 0.5;
        return CGRectMake(0, y, w, h);
    } else {
        CGFloat h = vs.height, w = h * ir, x = (vs.width - w) * 0.5;
        return CGRectMake(x, 0, w, h);
    }
}


/**
 将遮罩图,转换成带有透明度的
 */
/// 生成 α 遮罩：
///   • 涂抹区域  → 透明（α = 0）
///   • 未涂抹区域→ 不透明白（α = 255）
- (UIImage *)inpaintingAlphaMaskImage {
    UIImage *bw = self.maskImage;              // 黑底白线：白 = 涂抹
    if (!bw) return nil;

    CGSize sz = bw.size;
    UIGraphicsBeginImageContextWithOptions(sz, NO, bw.scale);
    CGContextRef ctx = UIGraphicsGetCurrentContext();

    // 1️⃣ 整张填白（不透明）
    [[UIColor whiteColor] setFill];
    CGContextFillRect(ctx, CGRectMake(0, 0, sz.width, sz.height));

    // 2️⃣ UIKit → CG 坐标翻转
    CGContextTranslateCTM(ctx, 0, sz.height);
    CGContextScaleCTM(ctx, 1.0, -1.0);

    // 3️⃣ 在 “白线” 区裁剪并清空 → α = 0
    CGContextSaveGState(ctx);
    CGContextClipToMask(ctx, CGRectMake(0, 0, sz.width, sz.height), bw.CGImage);
    CGContextClearRect(ctx, CGRectMake(0, 0, sz.width, sz.height));
    CGContextRestoreGState(ctx);

    UIImage *alphaMask = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return alphaMask;   // 结果：涂抹区域透明，其余白底
}







@end
