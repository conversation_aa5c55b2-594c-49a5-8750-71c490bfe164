//
//  CustomSmudgeImageView.h
//  HaiJieFu
//
//  v4 — 2025‑07‑07
//  + 支持两种工具模式：Erase（擦除）/ Brush（恢复）
//  + 新增 clearMask 方法，一键清空所有涂抹
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 涂抹工具模式
typedef NS_ENUM(NSInteger, SmudgeToolMode) {
    SmudgeToolModeErase,   ///< 默认。擦除 = 置透明，mask 画白
    SmudgeToolModeBrush    ///< 恢复 = 恢复原图，mask 画黑
};

@interface CustomSmudgeImageView : UIImageView

@property (nonatomic, strong, readonly) UIImage *originalImage;     ///< 原始图（只读）
@property (nonatomic, strong, readonly) UIImage *maskImage;         ///< 黑白遮罩图
@property (nonatomic, strong, readonly) UIImage *previewImage;      ///< 预览图 = self.image

@property (nonatomic, assign) CGFloat lineWidth;                    ///< 画笔粗细
@property (nonatomic, assign) SmudgeToolMode toolMode;              ///< 当前工具模式

/// 统一配置入口（首编 / 续编）
- (void)configureWithOriginalImage:(UIImage *)image mask:(nullable UIImage *)mask;

- (void)undoLastSmudge;   ///< 撤销一步
- (void)restoreOriginal;  ///< 清空全部涂抹（原图 + 全黑遮罩）


/// 生成带 Alpha 通道的遮罩 PNG
- (UIImage *)inpaintingAlphaMaskImage;


@end

NS_ASSUME_NONNULL_END
