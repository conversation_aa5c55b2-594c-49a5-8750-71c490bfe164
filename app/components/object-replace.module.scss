@import "../styles/animation.scss";

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--white);
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
  position: relative;
  z-index: 10;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.headerRight {
  display: flex;
  align-items: center;
}

.myWorksButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

// 错误提示样式
.errorMessage {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  margin: 16px 24px;
  border-radius: 8px;
  font-size: 14px;
  animation: slideDown 0.3s ease-out;

  .errorClose {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(220, 38, 38, 0.1);
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.backButton {
  margin-right: 16px;

  &:hover {
    background: var(--hover-color);
  }
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: var(--black);
  margin: 0;
}

.mainContainer {
  display: flex;
  flex: 1;
  overflow: hidden;
}

// 左侧案例展示区域 - 占更大宽度
.exampleArea {
  flex: 3; // 占2/3的宽度
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 右侧操作区域 - 相对较小
.operationArea {
  flex: 2; // 占1/3的宽度
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--white);
}



.exampleItem {
  background: transparent;
  border-radius: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

.exampleImages {
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.beforeAfter {
  display: flex;
  align-items: stretch;
  gap: 0;
  position: relative;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  min-height: 180px;
}

.imageContainer {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fafbfc, #f1f3f4);

  img {
    width: 100%;
    object-fit: contain;
    border: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  }

  &:first-child {
    border-radius: 12px 0 0 12px;
    background: linear-gradient(135deg, #fafbfc, #f5f6f7);
  }

  &:last-child {
    border-radius: 0 12px 12px 0;
    background: linear-gradient(135deg, #f8faf9, #f0f4f1);
  }
}

.imageLabel {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.75);
  color: white;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

.arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: #888;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.02);
}

.exampleDescription {
  p {
    font-size: 14px;
    color: var(--text-color-secondary);
    line-height: 1.4;
    margin: 0;
  }
}





.editSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.contentContainer {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.tabNavigation {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: 12px 0;
  margin-right: 32px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-secondary);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  background: none;
  border: none;

  &.activeTab {
    color: var(--primary);

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--primary);
      border-radius: 1px;
    }
  }

  &:hover:not(.activeTab) {
    color: var(--text-color);
  }
}

.tipSection {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #e0f2fe;
  border-radius: 8px;
  margin-bottom: 24px;
}

.tipIcon {
  font-size: 16px;
  margin-right: 8px;
}

.tipText {
  font-size: 14px;
  color: #0277bd;
}

.uploadSection {
  margin-bottom: 24px;
}

.uploadLabel {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 12px;
}

.required {
  color: #ef4444;
}

.uploadArea {
  border: 2px dashed rgba(29, 147, 171, 0.3);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--gray), rgba(29, 147, 171, 0.02));
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(29, 147, 171, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    border-color: var(--primary);
    background: linear-gradient(135deg, rgba(29, 147, 171, 0.05), rgba(29, 147, 171, 0.08));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(29, 147, 171, 0.15);

    &::before {
      left: 100%;
    }

    .uploadIcon svg {
      transform: scale(1.1);
      color: var(--primary);
    }
  }
}

.uploadPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 60px 24px;
  text-align: center;
}

.uploadIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 4px;

  svg {
    color: #d1d5db;
    transition: all 0.3s ease;
  }

  &:hover {
    svg {
      color: #9ca3af;
      transform: scale(1.05);
    }
  }
}

.uploadText {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 400;
  line-height: 1.4;
  margin: 0;
}

.imagePreview {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;

  img {
    max-width: 160px;
    max-height: 160px;
    border-radius: 12px;
    object-fit: contain;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

// 图片控制按钮样式 - 与图片编辑页面保持一致
.imageControls {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
  z-index: 10;
}

.controlButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  pointer-events: auto;
  color: white;
  font-size: 12px;
  font-weight: 500;

  svg {
    width: 14px;
    height: 14px;
    color: white;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
  }
}

.inpaintButton {
  width: auto;
  height: 28px;
  padding: 0 12px;
  gap: 4px;
  border-radius: 14px;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
}

.deleteButton {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  pointer-events: auto;
  color: white;
  position: relative;

  svg {
    width: 14px;
    height: 14px;
    color: white !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    path {
      stroke: white !important;
      fill: white !important;
    }
  }

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
  }
}



.debugInfo {
  margin-bottom: 16px;
  text-align: center;
}

.debugButton {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
}

.processButton {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;

  &.enabled {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
  }

  &.disabled {
    background: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

.bottomInfo {
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.credits {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  color: var(--text-color-secondary);
  text-align: center;
  margin-bottom: 8px;
}

.creditsCost {
  color: #374151;
  font-weight: 600;
}

.creditsDivider {
  color: #9ca3af;
  font-weight: bold;
  margin: 0 4px;
}

.creditsLink {
  color: #3b82f6;
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    color: #1d4ed8;
    text-decoration: underline;
  }
}

.resultSection {
  margin-top: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.resultImage {
  text-align: center;

  img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.resultPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.placeholderText {
  font-size: 14px;
  color: #9ca3af;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .exampleArea {
    flex: 1.5; // 在中等屏幕上稍微减少案例区域的比例
  }
}

@media (max-width: 768px) {
  .mainContainer {
    flex-direction: column;
  }

  .exampleArea {
    width: 100%;
    max-height: 45vh;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 20px;
    gap: 20px;
  }

  .operationArea {
    flex: 1;
  }

  .beforeAfterContainer {
    flex-direction: column;
    gap: 24px;
  }

  .beforeAfterImage {
    height: 200px;
  }

  .arrowIcon {
    transform: rotate(90deg);
    font-size: 24px;
  }

  .materialSection {
    padding: 20px;
  }

  .materialGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .materialImageWrapper {
    height: 100px;
  }

  .imageContainer img {
    height: 80px;
  }

  .uploadArea {
    min-height: 150px;
    padding: 16px;
  }

  .uploadPlaceholder {
    padding: 40px 16px;
  }

  .resultSection {
    margin-top: 16px;
    padding: 16px;
  }
}

// 案例展示样式 - 美观简约
.exampleSection {
  margin-bottom: 32px;

  h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--black);
    position: relative;
    padding-left: 0;
    letter-spacing: -0.02em;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 24px;
      height: 2px;
      background: linear-gradient(90deg, var(--primary), rgba(59, 130, 246, 0.3));
      border-radius: 1px;
    }
  }
}

.exampleContainer {
  display: flex;
  gap: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.exampleImage {
  flex: 1;
  height: 320px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &:first-child {
    border-right: 1px solid rgba(0, 0, 0, 0.08);
  }

  img {
    max-width: calc(100% - 16px);
    max-height: calc(100% - 16px);
    object-fit: contain;
    display: block;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

// 素材展示样式 - 美观简约
.materialSection {
  margin-bottom: 24px;

  h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--black);
    position: relative;
    padding-left: 0;
    letter-spacing: -0.02em;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 24px;
      height: 2px;
      background: linear-gradient(90deg, var(--primary), rgba(59, 130, 246, 0.3));
      border-radius: 1px;
    }
  }
}

.materialGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.materialItem {
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}

.materialImageWrapper {
  width: 100%;
  height: 100px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.05);

  img {
    max-width: calc(100% - 8px);
    max-height: calc(100% - 8px);
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }
}



// 局部重绘弹框样式
.inpaintModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.inpaintModalContent {
  background: white;
  border-radius: 16px;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

.inpaintHeader {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1000;
}

.inpaintCloseButton {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  pointer-events: auto;
  color: white;
  position: relative;

  svg {
    width: 14px;
    height: 14px;
    color: white !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    path {
      stroke: white !important;
      fill: white !important;
    }
  }

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
  }
}

.inpaintBody {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.inpaintCanvas {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  padding: 20px;
  overflow: hidden;
  min-height: 0;

  .canvasContainer {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: calc(100vw - 80px);
    max-height: calc(90vh - 180px);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      display: block;
    }

    canvas {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 8px;
      cursor: crosshair;
      pointer-events: auto;
    }
  }
}

.inpaintToolbar {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  flex-shrink: 0;
  min-height: 80px;
}

.toolbarLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbarRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolSection {
  display: flex;
  align-items: center;
  gap: 8px;

  .toolIcon {
    width: 20px;
    height: 20px;
    color: #6b7280;
  }

  .toolLabel {
    font-size: 14px;
    color: #6b7280;
    margin-right: 8px;
  }
}

.autoDetectButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #6b7280;

  .toolIcon {
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
  }
}

.brushSizeControl {
  display: flex;
  align-items: center;
  gap: 12px;

  .sizeSlider {
    width: 120px;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    outline: none;
    appearance: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: #3b82f6;
      border-radius: 50%;
      cursor: pointer;
    }

    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: #3b82f6;
      border-radius: 50%;
      border: none;
      cursor: pointer;
    }
  }

  .sizeDisplay {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
    min-width: 30px;
    text-align: center;
  }
}

.toolButtons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  svg {
    width: 18px;
    height: 18px;
    color: #6b7280;
  }

  &:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
  }

  &.active {
    background: #3b82f6;
    border-color: #3b82f6;

    svg {
      color: white;
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background: #f9fafb;
      border-color: #e5e7eb;
    }
  }
}

.confirmButton {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #2563eb;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background: #3b82f6;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .inpaintModalContent {
    width: 95vw;
    height: 95vh;
  }

  .inpaintCanvas {
    padding: 10px;

    .canvasContainer {
      max-width: calc(100vw - 20px);
      max-height: calc(95vh - 180px);

      img {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
      }

      canvas {
        width: 100%;
        height: 100%;
      }
    }
  }

  .inpaintToolbar {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    min-height: auto;
  }

  .toolbarLeft,
  .toolbarRight {
    width: 100%;
    justify-content: center;
  }

  .brushSizeControl .sizeSlider {
    width: 100px;
  }
}

// Tab相关样式
.tabContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.immediateTab {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.resultTab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 处理中的结果样式
.processingResult {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.resultHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 8px;
}

.resultMeta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.resultDate {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.resultVersion {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
}

.resultActions {
  display: flex;
  gap: 8px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  svg {
    width: 16px;
    height: 16px;
    color: #6b7280;
  }

  &:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.processingStatus {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.waitingText {
  font-size: 13px;
  color: #f59e0b;
  font-weight: 500;
}

.processingBadge {
  background: #6b7280;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.2s ease;

  &:hover {
    background: #4b5563;
  }
}

.resultRuntime {
  font-size: 13px;
  color: #9ca3af;
  font-family: 'Monaco', 'Menlo', monospace;
}

.resultStatus {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.statusText {
  font-size: 13px;
  color: #6b7280;
}

.statusBadge {
  background: #3b82f6;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.resultContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.resultImageWrapper {
  margin-top: 8px;
}

.resultImage {
  width: 200px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.emptyResult {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 60px 20px;
  color: var(--text-color-secondary);

  .emptyIcon {
    font-size: 48px;
    opacity: 0.5;
  }

  h3 {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin: 0;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

// 状态容器 - 极简设计
.statusContainer {
  padding: 16px;
}

// 处理中卡片 - 简约美观
.processingCard {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
}

.statusRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.statusLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 加载动画 - 小而精致
.spinner {
  width: 12px;
  height: 12px;
  border: 1.5px solid #e5e5e5;
  border-top: 1.5px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statusLabel {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.timeLabel {
  font-size: 11px;
  color: #9ca3af;
}

// 成功卡片 - 清新绿色
.successCard {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
}

.successRow {
  display: flex;
  align-items: center;
  gap: 12px;
}

.checkIcon {
  width: 20px;
  height: 20px;
  background: #22c55e;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.successText {
  flex: 1;
}

.successTitle {
  font-size: 14px;
  font-weight: 500;
  color: #166534;
  margin-bottom: 4px;
}

.successDesc {
  font-size: 12px;
  color: #15803d;
  line-height: 1.3;
}