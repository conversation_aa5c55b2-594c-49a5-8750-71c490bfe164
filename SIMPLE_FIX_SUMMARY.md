# 万物替换涂抹检测简单修复

## 🎯 问题
涂抹完依然提示"请先对原图进行重绘标记需要替换的区域"

## 🔍 根本原因
`prepareFiles` 函数的依赖数组中缺少 `hasCanvasContent`，导致函数中使用的是过时的 `hasCanvasContent` 引用。

## ✅ 修复
在 `prepareFiles` 的依赖数组中添加 `hasCanvasContent`：

```tsx
// 修复前
}, [
  originalImageFile,
  sourceImage,
  replaceImageFile,
  targetImage,
  base64ToFile,
  createMaskImage,
]);

// 修复后
}, [
  originalImageFile,
  sourceImage,
  replaceImageFile,
  targetImage,
  base64ToFile,
  createMaskImage,
  hasCanvasContent,  // ← 添加这个
]);
```

## 🎯 现在应该正常工作了
涂抹后点击Go按钮应该能正常进入处理流程。
