<!DOCTYPE html>
<html>
<head>
    <title>Canvas Detection Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Canvas Content Detection Test</h1>
    
    <canvas id="testCanvas" width="300" height="200"></canvas>
    <br>
    
    <button onclick="drawSomething()">Draw Something</button>
    <button onclick="clearCanvas()">Clear Canvas</button>
    <button onclick="checkContent()">Check Content</button>
    
    <div id="result" class="result">
        Click "Check Content" to test detection
    </div>

    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const resultDiv = document.getElementById('result');

        // 复制万物替换页面的检测逻辑
        function hasCanvasContent(canvas) {
            const ctx = canvas.getContext("2d");
            if (!ctx) {
                console.log("❌ 没有画布上下文");
                return false;
            }

            // 检查画布尺寸
            if (canvas.width === 0 || canvas.height === 0) {
                console.log("❌ 画布尺寸为0");
                return false;
            }

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            console.log("📊 画布数据长度:", imageData.data.length);
            console.log("📊 画布尺寸:", canvas.width, "x", canvas.height);

            // 检查前几个有alpha值的像素
            let alphaPixels = 0;
            let totalNonZeroAlpha = 0;
            for (let i = 0; i < imageData.data.length && alphaPixels < 10; i += 4) {
                if (imageData.data[i + 3] > 0) {
                    alphaPixels++;
                    totalNonZeroAlpha++;
                    if (alphaPixels <= 5) {
                        console.log(`🎨 像素 ${i/4}: rgba(${imageData.data[i]}, ${imageData.data[i+1]}, ${imageData.data[i+2]}, ${imageData.data[i+3]})`);
                    }
                }
            }

            // 计算总的非零alpha像素数
            for (let i = alphaPixels * 4 + 3; i < imageData.data.length; i += 4) {
                if (imageData.data[i] > 0) {
                    totalNonZeroAlpha++;
                }
            }

            const hasContent = totalNonZeroAlpha > 0;

            console.log("🔍 检测结果:", hasContent, "找到alpha像素:", alphaPixels, "总非零alpha像素:", totalNonZeroAlpha);
            return hasContent;
        }

        function drawSomething() {
            // 模拟万物替换页面的绘制方式
            ctx.globalCompositeOperation = "source-over";
            ctx.strokeStyle = "rgba(255, 255, 255, 0.8)"; // 半透明白色
            ctx.lineWidth = 32;
            ctx.lineCap = "round";
            ctx.lineJoin = "round";
            
            ctx.beginPath();
            ctx.moveTo(50, 50);
            ctx.lineTo(150, 100);
            ctx.lineTo(100, 150);
            ctx.stroke();
            
            resultDiv.innerHTML = "Drew some lines with rgba(255, 255, 255, 0.8)";
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            resultDiv.innerHTML = "Canvas cleared";
        }

        function checkContent() {
            const hasContent = hasCanvasContent(canvas);
            resultDiv.innerHTML = `
                <strong>Detection Result: ${hasContent ? 'HAS CONTENT' : 'NO CONTENT'}</strong><br>
                Canvas size: ${canvas.width}x${canvas.height}<br>
                Check console for detailed logs
            `;
        }

        // 初始检查
        checkContent();
    </script>
</body>
</html>
