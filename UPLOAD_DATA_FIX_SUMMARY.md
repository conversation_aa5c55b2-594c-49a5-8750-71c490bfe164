# 万物替换上传数据修复总结

## 🎯 发现的问题

### 1. 遮罩图生成错误
**问题**: 万物替换直接使用 `maskCanvas` 的 base64 数据作为遮罩文件
```tsx
// ❌ 错误的做法
maskFile = base64ToFile(maskCanvas, "mask.png");
```

**正确做法**: 应该像图片编辑一样，使用 `createMaskImage(canvas)` 生成正确的遮罩格式
```tsx
// ✅ 正确的做法
maskFile = createMaskImage(canvas);
```

### 2. 遮罩内容检查不一致
**问题**: 万物替换只检查 `maskCanvas` 状态是否存在
```tsx
// ❌ 错误的检查
if (!maskCanvas) {
  showError("请先对原图进行重绘标记需要替换的区域");
  return;
}
```

**正确做法**: 应该检查画布是否有实际绘制内容
```tsx
// ✅ 正确的检查
const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
const hasContent = imageData.data.some(
  (value, index) => index % 4 === 3 && value > 0,
);
```

## ✅ 修复内容

### 1. 修复 prepareFiles 函数
**修复前**:
```tsx
// 准备遮罩文件 (从重绘画布生成)
if (maskCanvas) {
  maskFile = base64ToFile(maskCanvas, "mask.png");
} else {
  throw new Error("请先对原图进行重绘标记需要替换的区域");
}
```

**修复后**:
```tsx
// 准备遮罩文件 (从重绘画布生成)
const canvas = canvasRef.current;
if (canvas) {
  const ctx = canvas.getContext("2d");
  if (ctx) {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const hasContent = imageData.data.some(
      (value, index) => index % 4 === 3 && value > 0,
    );
    if (hasContent) {
      maskFile = createMaskImage(canvas);
    } else {
      throw new Error("请先对原图进行重绘标记需要替换的区域");
    }
  } else {
    throw new Error("无法获取画布上下文");
  }
} else {
  throw new Error("请先对原图进行重绘标记需要替换的区域");
}
```

### 2. 修复 handleProcess 函数
**修复前**:
```tsx
if (!maskCanvas) {
  showError("请先对原图进行重绘标记需要替换的区域");
  return;
}
```

**修复后**:
```tsx
// 检查是否有遮罩内容
const canvas = canvasRef.current;
if (!canvas) {
  showError("请先对原图进行重绘标记需要替换的区域");
  return;
}

const ctx = canvas.getContext("2d");
if (!ctx) {
  showError("无法获取画布上下文");
  return;
}

const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
const hasContent = imageData.data.some(
  (value, index) => index % 4 === 3 && value > 0,
);

if (!hasContent) {
  showError("请先对原图进行重绘标记需要替换的区域");
  return;
}
```

### 3. 更新依赖数组
**修复前**:
```tsx
}, [
  originalImageFile,
  sourceImage,
  maskCanvas,
  replaceImageFile,
  targetImage,
  base64ToFile,
]);
```

**修复后**:
```tsx
}, [
  originalImageFile,
  sourceImage,
  replaceImageFile,
  targetImage,
  base64ToFile,
  createMaskImage,
]);
```

## 🔧 技术细节

### 遮罩生成流程
1. **画布绘制**: 用户在画布上绘制遮罩区域
2. **黑白遮罩**: `createBlackWhiteMask(canvas)` 创建黑白遮罩
3. **Alpha遮罩**: `createAlphaMaskImage(blackWhiteMask)` 创建最终遮罩文件
4. **文件上传**: 上传正确格式的遮罩文件

### 上传数据结构
```tsx
// 三个文件都正确生成和上传
const uploadResults = {
  originalResult: { name: "original_image_name" },  // 原图
  maskResult: { name: "mask_image_name" },          // 遮罩图（正确格式）
  replaceResult: { name: "replace_image_name" }     // 替换图
};

// API 请求参数
const replaceRequest: FluxAnythingReplaceRequest = {
  originalImageName: uploadResults.originalResult.name,
  maskImageName: uploadResults.maskResult.name,
  replaceImageName: uploadResults.replaceResult.name,
  handleType: HandleType.ANYTHING_REPLACE,
};
```

## 🎉 修复效果

### 修复前的问题
- ❌ 遮罩图格式错误（直接使用画布 base64）
- ❌ 遮罩内容检查不准确
- ❌ 可能导致 API 处理失败

### 修复后的效果
- ✅ 遮罩图格式正确（使用 createMaskImage 生成）
- ✅ 遮罩内容检查准确（检查实际像素数据）
- ✅ 与图片编辑完全一致的处理逻辑
- ✅ API 能正确处理上传的数据

---

**修复完成！现在万物替换的上传数据处理与图片编辑完全一致，确保 API 能正确处理原图、遮罩图和替换图。**
